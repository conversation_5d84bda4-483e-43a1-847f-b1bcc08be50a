import 'dart:async';
import 'package:get/get.dart';
import 'package:rolio/common/utils/logger.dart';

/// 全局事件状态管理器
///
/// 使用GetX响应式变量管理全局事件状态，替代EventBus
class GlobalEventState extends GetxController {
  static GlobalEventState get to => Get.find<GlobalEventState>();

  // 已移除Timer管理集合，使用Future.delayed替代

  @override
  void onInit() {
    super.onInit();
    LogUtil.info('GlobalEventState初始化完成');
  }

  @override
  void onReady() {
    super.onReady();
    LogUtil.debug('GlobalEventState准备就绪');
  }

  @override
  void onClose() {
    _cancelAllTimers();
    super.onClose();
    LogUtil.info('GlobalEventState资源清理完成');
  }

  // ==================== 认证相关事件状态 ====================
  
  /// Token过期状态
  final RxBool tokenExpired = false.obs;
  
  /// 认证流程处理状态
  final RxBool authProcessing = false.obs;
  
  /// Token验证状态
  final RxBool tokenValidated = false.obs;
  
  /// WebSocket连接允许状态
  final RxBool websocketConnectionAllowed = false.obs;

  // ==================== WebSocket相关事件状态 ====================
  
  /// WebSocket断开连接状态
  final RxBool websocketDisconnected = false.obs;
  
  /// AI回复重置事件
  final Rx<Map<String, dynamic>?> aiReplyReset = Rx<Map<String, dynamic>?>(null);
  
  /// WebSocket错误信息
  final Rx<Map<String, dynamic>?> websocketError = Rx<Map<String, dynamic>?>(null);

  // ==================== 消息相关事件状态 ====================
  
  /// 消息添加事件
  final Rx<Map<String, dynamic>?> messageAdded = Rx<Map<String, dynamic>?>(null);
  
  /// 消息更新事件
  final Rx<Map<String, dynamic>?> messageUpdated = Rx<Map<String, dynamic>?>(null);
  
  /// 消息删除事件
  final Rx<Map<String, dynamic>?> messageDeleted = Rx<Map<String, dynamic>?>(null);

  // ==================== 会话相关事件状态 ====================
  
  /// 会话更新事件
  final Rx<Map<String, dynamic>?> sessionUpdated = Rx<Map<String, dynamic>?>(null);
  
  /// 会话创建事件
  final Rx<Map<String, dynamic>?> sessionCreated = Rx<Map<String, dynamic>?>(null);
  
  /// 会话删除事件
  final Rx<Map<String, dynamic>?> sessionDeleted = Rx<Map<String, dynamic>?>(null);

  // ==================== 角色相关事件状态 ====================
  
  /// 角色消息事件
  final Rx<Map<String, dynamic>?> roleMessage = Rx<Map<String, dynamic>?>(null);
  
  /// 活跃角色变更事件
  final Rx<Map<String, dynamic>?> activeRoleChanged = Rx<Map<String, dynamic>?>(null);
  
  /// 角色会话绑定事件
  final Rx<Map<String, dynamic>?> roleConversationBound = Rx<Map<String, dynamic>?>(null);

  /// 用户身份变更事件
  final Rx<Map<String, dynamic>?> userIdentityChanged = Rx<Map<String, dynamic>?>(null);

  // ==================== 数据刷新事件状态 ====================
  
  /// 推荐数据刷新触发器
  final RxBool refreshRecommendData = false.obs;
  
  /// 会话数据刷新触发器
  final RxBool refreshSessionsData = false.obs;

  // ==================== 资源清理事件状态 ====================
  
  /// 角色资源清理事件
  final Rx<Map<String, dynamic>?> roleResourceCleanup = Rx<Map<String, dynamic>?>(null);

  /// 角色绑定清理事件
  final Rx<Map<String, dynamic>?> roleBindingCleanup = Rx<Map<String, dynamic>?>(null);

  /// 会话资源清理事件
  final Rx<Map<String, dynamic>?> conversationResourceCleanup = Rx<Map<String, dynamic>?>(null);

  /// 会话缓存清理事件
  final Rx<Map<String, dynamic>?> sessionCacheCleanup = Rx<Map<String, dynamic>?>(null);

  /// 取消所有活跃的Timer - 已移除，使用Future.delayed替代
  void _cancelAllTimers() {
    // 不再需要取消Timer，Future.delayed会自动完成
    LogUtil.debug('Timer管理已移除，使用Future.delayed替代');
  }

  /// 取消特定事件的自动重置
  void cancelAutoReset(String eventType) {
    // 这里可以根据需要实现特定事件的取消逻辑
    LogUtil.debug('取消事件自动重置: $eventType');
  }

  // ==================== 认证相关事件触发方法 ====================
  
  /// 触发Token过期事件
  void triggerTokenExpired({
    String? reason,
    Duration? autoResetDelay = const Duration(milliseconds: 100),
  }) {
    tokenExpired.value = true;
    LogUtil.info('触发Token过期事件${reason != null ? ', 原因: $reason' : ''}');

    // 可配置的自动重置状态 - 使用Future.delayed替代Timer
    if (autoResetDelay != null) {
      Future.delayed(autoResetDelay, () {
        tokenExpired.value = false;
      });
    }
  }
  
  /// 触发认证流程开始
  void triggerAuthProcessStarted({String? userId}) {
    authProcessing.value = true;
    LogUtil.info('触发认证流程开始事件${userId != null ? ', 用户ID: $userId' : ''}');
  }
  
  /// 触发认证流程完成
  void triggerAuthProcessCompleted({String? userId}) {
    authProcessing.value = false;
    LogUtil.info('触发认证流程完成事件${userId != null ? ', 用户ID: $userId' : ''}');
  }
  
  /// 触发Token验证
  void triggerTokenValidated({
    String? userId,
    int? tokenLength,
    Duration? autoResetDelay = const Duration(milliseconds: 100),
  }) {
    tokenValidated.value = true;
    LogUtil.info('触发Token验证事件${userId != null ? ', 用户ID: $userId' : ''}');

    // 可配置的自动重置状态 - 使用Future.delayed替代Timer
    if (autoResetDelay != null) {
      Future.delayed(autoResetDelay, () {
        tokenValidated.value = false;
      });
    }
  }

  // ==================== WebSocket相关事件触发方法 ====================
  
  /// 触发WebSocket断开连接事件
  void triggerWebsocketDisconnected({
    String? error,
    String? state,
    Duration? autoResetDelay = const Duration(milliseconds: 500),
  }) {
    websocketDisconnected.value = true;
    LogUtil.info('触发WebSocket断开连接事件${error != null ? ', 错误: $error' : ''}${state != null ? ', 状态: $state' : ''}');

    // 可配置的自动重置状态 - 使用Future.delayed替代Timer
    if (autoResetDelay != null) {
      Future.delayed(autoResetDelay, () {
        websocketDisconnected.value = false;
      });
    }
  }
  
  /// 触发AI回复重置事件
  void triggerAiReplyReset({
    String? eventId,
    int? roleId,
    Duration? autoResetDelay = const Duration(milliseconds: 300),
  }) {
    aiReplyReset.value = {
      'eventId': eventId,
      'roleId': roleId,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
    LogUtil.info('触发AI回复重置事件${eventId != null ? ', 事件ID: $eventId' : ''}${roleId != null ? ', 角色ID: $roleId' : ''}');

    // 可配置的自动重置状态 - 使用Future.delayed替代Timer
    if (autoResetDelay != null) {
      Future.delayed(autoResetDelay, () {
        aiReplyReset.value = null;
      });
    }
  }
  
  /// 触发WebSocket错误事件
  void triggerWebsocketError(Map<String, dynamic> errorData) {
    websocketError.value = errorData;
    LogUtil.info('触发WebSocket错误事件: ${errorData['errorType'] ?? 'UNKNOWN'}');
  }

  // ==================== 消息相关事件触发方法 ====================
  
  /// 触发消息添加事件
  void triggerMessageAdded(Map<String, dynamic> messageData) {
    messageAdded.value = messageData;
    LogUtil.debug('触发消息添加事件');
  }
  
  /// 触发消息更新事件
  void triggerMessageUpdated(Map<String, dynamic> messageData) {
    messageUpdated.value = messageData;
    LogUtil.debug('触发消息更新事件');
  }

  // ==================== 会话相关事件触发方法 ====================
  
  /// 触发会话更新事件
  void triggerSessionUpdated(int conversationId, String? lastMessage, DateTime? lastMessageTime) {
    sessionUpdated.value = {
      'conversationId': conversationId,
      'lastMessage': lastMessage,
      'lastMessageTime': lastMessageTime,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
    LogUtil.debug('触发会话更新事件: conversationId=$conversationId');
  }

  // ==================== 角色相关事件触发方法 ====================
  
  /// 触发角色消息事件
  void triggerRoleMessage(Map<String, dynamic> messageData) {
    roleMessage.value = messageData;
    LogUtil.debug('触发角色消息事件: roleId=${messageData['roleId']}');
  }

  /// 触发活跃角色变更事件
  void triggerActiveRoleChanged(
    int roleId,
    int? previousRoleId, {
    Duration? autoResetDelay = const Duration(milliseconds: 300),
  }) {
    activeRoleChanged.value = {
      'roleId': roleId,
      'previousRoleId': previousRoleId,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
    LogUtil.debug('触发活跃角色变更事件: roleId=$roleId, previousRoleId=$previousRoleId');

    // 可配置的自动重置状态 - 使用Future.delayed替代Timer
    if (autoResetDelay != null) {
      Future.delayed(autoResetDelay, () {
        activeRoleChanged.value = null;
      });
    }
  }

  /// 触发角色会话绑定事件
  void triggerRoleConversationBound(int roleId, int conversationId) {
    roleConversationBound.value = {
      'role_id': roleId,
      'conversation_id': conversationId,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
    LogUtil.debug('触发角色会话绑定事件: roleId=$roleId, conversationId=$conversationId');
  }

  // ==================== 数据刷新事件触发方法 ====================
  
  /// 触发推荐数据刷新
  void triggerRefreshRecommendData({bool forceRefresh = true, String? source}) {
    refreshRecommendData.toggle();
    LogUtil.info('触发推荐数据刷新${source != null ? ', 来源: $source' : ''}');
  }
  
  /// 触发会话数据刷新
  void triggerRefreshSessionsData({bool forceRefresh = true, String? source}) {
    refreshSessionsData.toggle();
    LogUtil.info('触发会话数据刷新${source != null ? ', 来源: $source' : ''}');
  }

  /// 触发用户身份变更事件
  void triggerUserIdentityChanged({
    String? userId,
    Duration? autoResetDelay = const Duration(milliseconds: 500),
  }) {
    userIdentityChanged.value = {
      'userId': userId,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
    LogUtil.info('触发用户身份变更事件${userId != null ? ', userId: $userId' : ''}');

    // 可配置的自动重置状态 - 使用Future.delayed替代Timer
    if (autoResetDelay != null) {
      Future.delayed(autoResetDelay, () {
        userIdentityChanged.value = null;
      });
    }
  }

  // ==================== 资源清理事件触发方法 ====================
  
  /// 触发角色资源清理事件
  void triggerRoleResourceCleanup(int roleId, {int? conversationId, String? reason}) {
    roleResourceCleanup.value = {
      'roleId': roleId,
      'conversationId': conversationId,
      'reason': reason,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
    LogUtil.debug('触发角色资源清理事件: roleId=$roleId${reason != null ? ', 原因: $reason' : ''}');
  }

  /// 触发角色绑定清理事件
  void triggerRoleBindingCleanup(int roleId, {int? conversationId}) {
    roleBindingCleanup.value = {
      'roleId': roleId,
      'conversationId': conversationId,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
    LogUtil.debug('触发角色绑定清理事件: roleId=$roleId, conversationId=$conversationId');
  }

  /// 触发会话资源清理事件
  void triggerConversationResourceCleanup(int conversationId, {String? reason}) {
    conversationResourceCleanup.value = {
      'conversationId': conversationId,
      'reason': reason,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
    LogUtil.debug('触发会话资源清理事件: conversationId=$conversationId${reason != null ? ', 原因: $reason' : ''}');
  }

  /// 触发会话缓存清理事件
  void triggerSessionCacheCleanup(int conversationId, {String? reason}) {
    sessionCacheCleanup.value = {
      'conversationId': conversationId,
      'reason': reason,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
    LogUtil.debug('触发会话缓存清理事件: conversationId=$conversationId${reason != null ? ', 原因: $reason' : ''}');
  }
}
