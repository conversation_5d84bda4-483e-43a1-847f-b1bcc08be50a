import 'package:firebase_auth/firebase_auth.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';

import 'package:rolio/common/models/user.dart' as app;
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/utils/error_handler.dart';
import 'package:rolio/common/constants/error_codes.dart';
import 'package:rolio/manager/global_state.dart';
import 'package:rolio/modules/login/repository/login_repository.dart';
import 'package:connectivity_plus/connectivity_plus.dart' as connectivity;
import 'package:rolio/common/services/cache_cleanup_service.dart';
import 'package:rolio/common/state/global_event_state.dart';
import 'package:rolio/manager/ws_manager.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:rolio/modules/sessions/service/session_service.dart';
import 'package:rolio/common/utils/message_tracker.dart';
import 'package:rolio/common/services/session_binding_service.dart';
import 'package:rolio/modules/role/service/recommend_service.dart';
import 'package:rolio/modules/role/controller/recommend_controller.dart';
import 'package:rolio/common/cache/secure_storage.dart';
import 'dart:math' as math;

/// 登录服务
class LoginService extends GetxService with WidgetsBindingObserver {
  /// Firebase Auth实例
  final FirebaseAuth _auth = FirebaseAuth.instance;
  
  /// 登录仓库
  final LoginRepository _repository;
  
  /// 全局状态
  final GlobalState _globalState = Get.find<GlobalState>();
  
  /// Token获取去重 - 上次获取时间
  int _lastTokenFetchTime = 0;

  /// Token获取去重 - 最小间隔时间(毫秒)
  final int _tokenFetchMinInterval = 500; // 0.5秒

  /// 响应式Token获取状态
  final RxBool isTokenFetching = false.obs;

  /// 响应式最后Token获取时间
  final Rx<DateTime?> lastTokenFetchTime = Rx<DateTime?>(null);
  
  /// Token获取去重 - 是否有获取操作正在进行
  bool _isTokenFetchInProgress = false;
  
  /// 待处理的Token获取请求状态 - 使用GetX响应式变量
  final RxMap<String, bool> _pendingTokenRequests = <String, bool>{}.obs;

  /// Token获取结果 - 使用GetX响应式变量
  final Rxn<String> _tokenResult = Rxn<String>();
  
  /// 构造函数
  LoginService({required LoginRepository repository}) : _repository = repository;

  // 安全存储实例
  final _secureStorage = Get.find<SecureStorage>();

  @override
  void onInit() {
    super.onInit();
    // 注册应用生命周期监听
    WidgetsBinding.instance.addObserver(this);
    // 初始监听认证状态
    listenAuthStateChanges();
    // 尝试从安全存储加载token
    _loadTokenFromSecureStorage();
  }

  /// 从安全存储加载token
  Future<void> _loadTokenFromSecureStorage() async {
    try {
      final storedToken = await _secureStorage.getToken();
      if (storedToken != null && storedToken.isNotEmpty) {
        // 设置到全局状态
        await _globalState.setAccessToken(storedToken);
        LogUtil.debug('已从安全存储加载token到全局状态，长度: ${storedToken.length}');
      }
    } catch (e) {
      LogUtil.error('从安全存储加载token失败: $e');
    }
  }

  @override
  void onClose() {
    // 移除应用生命周期监听
    WidgetsBinding.instance.removeObserver(this);
    super.onClose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // 当应用从后台恢复到前台时，检查并同步认证状态
    if (state == AppLifecycleState.resumed) {
      LogUtil.info('应用回到前台，同步认证状态');
      _syncAuthState();
    }
  }

  /// 同步认证状态
  Future<void> _syncAuthState() async {
    try {
      final firebaseUser = _auth.currentUser;
      
      // 如果Firebase没有用户，但全局状态有用户，则清除全局状态
      if (firebaseUser == null && _globalState.currentUser.value != null) {
        LogUtil.info('检测到认证状态不一致：Firebase无用户，但全局状态有用户，正在清除');
        await _globalState.clearCurrentUser();
        // 确保有匿名用户登录
        await ensureAnonymousLogin();
      } 
      // 如果Firebase有用户，但全局状态无用户或用户ID不匹配，则更新全局状态
      else if (firebaseUser != null && 
          (_globalState.currentUser.value == null || 
          _globalState.currentUser.value!.uid != firebaseUser.uid)) {
        LogUtil.info('检测到认证状态不一致：更新全局用户状态');
        _updateUserState(firebaseUser);
      }
      
      // 如果Firebase有用户，确保Token是最新的
      if (firebaseUser != null) {
        final currentToken = _globalState.accessToken.value;
        final newToken = await firebaseUser.getIdToken(true);
        
        // 只有当token确实变化时才更新并触发重连
        if (newToken != null && newToken != currentToken) {
          LogUtil.debug('Token已更新，旧token: ${currentToken?.substring(0, 10)}..., 新token: ${newToken.substring(0, 10)}...');
          await _globalState.setAccessToken(newToken);
          
          // 触发WebSocket重连，但增加延迟避免频繁重连
          await Future.delayed(const Duration(milliseconds: 500));
          _notifyWebSocketReconnect();
        } else {
          LogUtil.debug('Token未变化，跳过WebSocket重连');
        }
      }
    } catch (e) {
      LogUtil.error('同步认证状态失败: $e');
      ErrorHandler.handleException(
        AuthException('failed to sync auth state', originalError: e, code: ErrorCodes.AUTH_ERROR),
        showSnackbar: false,
      );
    }
  }
  
  /// 更新角色会话绑定（用于登录成功后调用）
  Future<void> _updateRoleSessionBindings() async {
    if (Get.isRegistered<SessionBindingService>()) {
      try {
        final sessionBindingService = Get.find<SessionBindingService>();
        await sessionBindingService.handleUserStateChange(refreshRecommendData: true);
        LogUtil.debug('用户登录后已更新角色会话绑定');
      } catch (e) {
        LogUtil.error('处理用户状态变化失败: $e');
      }
    }
  }

  /// 登录后的通用处理
  Future<void> _processAfterLogin(User? user) async {
    if (user == null) {
      LogUtil.warn('登录后处理：用户对象为空');
      return;
    }
    
    // 触发认证开始事件
    GlobalEventState.to.triggerAuthProcessStarted(userId: user.uid);
    LogUtil.debug('发送auth_process_started事件，userId: ${user.uid}');
    
    // 先清除旧的token，确保不会使用缓存的token
    await _globalState.clearAccessToken();
    LogUtil.debug('已清除旧token，准备获取新用户token');
    
    // 创建用户对象并设置到全局状态
    final appUser = app.User.fromFirebaseUser(user);
    final oldUserId = _globalState.currentUser.value?.uid;
    _globalState.setCurrentUser(appUser);
    
    LogUtil.info('用户状态已更新: ${appUser.uid}, 是否匿名: ${user.isAnonymous}');
    
    // 强制获取新用户的token
    final token = await getIdToken(forceRefresh: true);
    
    if (token != null && token.isNotEmpty) {
      LogUtil.info('登录成功，已获取并设置新用户token，长度: ${token.length}');
      
      // 验证token是否确实属于当前用户
      await _validateTokenAndNotify(token, user.uid);
    } else {
      LogUtil.error('登录后获取token失败，这会导致WebSocket连接问题');
    }
    
    // 等待一段时间确保token已经完全应用
    await Future.delayed(const Duration(milliseconds: 300));
    
    // 更新角色会话绑定
    await _updateRoleSessionBindings();
    
    // 刷新推荐数据
    await _refreshRecommendDataAfterLogin();
    
    // 清空并刷新会话列表
    await _refreshSessionsAfterLogin();
    
    // 触发认证完成事件
    GlobalEventState.to.triggerAuthProcessCompleted(userId: user.uid);
    LogUtil.debug('发送auth_process_completed事件，userId: ${user.uid}');
    
    // 用户ID变更时触发WebSocket重连
    // 这里不再需要直接触发重连，将通过监听auth_process_completed事件来处理
    if (oldUserId != appUser.uid) {
      LogUtil.info('用户ID已变更: $oldUserId -> ${appUser.uid}，WebSocket重连将由认证流程完成事件处理');
    }
  }

  /// 刷新会话列表
  Future<void> _refreshSessionsAfterLogin() async {
    try {
      // 触发刷新会话数据事件
      GlobalEventState.to.triggerRefreshSessionsData(
        forceRefresh: true,
        source: 'login_success'
      );
      LogUtil.info('已发送会话列表刷新事件');
      
      // 先清空会话列表缓存
      if (Get.isRegistered<SessionService>()) {
        LogUtil.debug('登录后清空会话列表缓存');
        final sessionService = Get.find<SessionService>();
        await sessionService.clearCache();
        
        // 刷新会话列表
        await sessionService.refreshSessions(refresh: true);
        LogUtil.debug('登录后已刷新会话列表');
      }
    } catch (e) {
      LogUtil.error('登录后刷新会话列表失败: $e');
    }
  }
  
  

  
  /// 用户退出
  Future<void> logoutUser() async {
    try {
      LogUtil.info('开始用户登出流程...');
      
      // 触发认证开始事件
      GlobalEventState.to.triggerAuthProcessStarted();
      LogUtil.debug('发送auth_process_started事件，开始登出流程');
      
      // 1. 清除全局状态中的token和用户信息
      LogUtil.debug('清除全局状态中的token和用户信息...');
      await _globalState.clearCurrentUser();
      await _globalState.clearAccessToken();
      
      // 从安全存储中删除token
      await _secureStorage.deleteToken();
      LogUtil.debug('已从安全存储中删除token');
      
      // 2. 断开WebSocket连接
      try {
        LogUtil.debug('断开WebSocket连接...');
        final wsManager = Get.find<WsManager>();
        await wsManager.disconnect();
        LogUtil.debug('已断开WebSocket连接');
      } catch (e) {
        LogUtil.error('断开WebSocket连接失败，继续登出流程: $e');
      }
      // 3. 清理缓存数据
      LogUtil.debug('清理缓存数据...');
      await _cleanupCaches();
      
      // 4. 显式清理会话绑定和会话列表缓存
      try {
        // 清除会话绑定关系
        if (Get.isRegistered<SessionBindingService>()) {
          LogUtil.debug('显式清理所有会话绑定关系...');
          final bindingService = Get.find<SessionBindingService>();
          await bindingService.clearAllBindings();
        }
        
        // 清除会话列表缓存
        if (Get.isRegistered<SessionService>()) {
          LogUtil.debug('显式清理会话列表缓存...');
          final sessionService = Get.find<SessionService>();
          await sessionService.clearCache();
          sessionService.sessions.clear(); // 确保会话列表被清空
          LogUtil.debug('会话列表已清空');
        }
        
        LogUtil.info('会话绑定和会话列表缓存已显式清理');
      } catch (e) {
        LogUtil.error('显式清理会话缓存失败: $e');
      }
      
      // 触发认证完成事件
      GlobalEventState.to.triggerAuthProcessCompleted();
      LogUtil.debug('发送auth_process_completed事件，登出流程完成');
    } catch (e) {
      LogUtil.error('用户登出失败: $e');
      ErrorHandler.handleException(
        AuthException('failed to logout', originalError: e, code: ErrorCodes.AUTH_ERROR),
        showSnackbar: true,
      );
      rethrow;
    }
  }
  
  /// 清理缓存数据
  Future<void> _cleanupCaches() async {
    try {
      LogUtil.info('开始清理所有缓存数据...');
      
      // 使用CacheCleanupService统一清理所有缓存
      if (Get.isRegistered<CacheCleanupService>()) {
        final cacheCleanupService = Get.find<CacheCleanupService>();
        // 清理所有缓存，包括SharedPreferences、消息缓存、待处理消息等
        // 登录/退出时不关闭WebSocket连接，因为后续需要重新建立连接
        final success = await cacheCleanupService.clearAllCachesCompletely(closeWebSocket: false);
        
        if (success) {
          LogUtil.info('所有缓存清理成功');
        } else {
          LogUtil.warn('部分缓存清理失败，但继续执行流程');
        }
      } else {
        LogUtil.warn('CacheCleanupService未注册，使用备用清理方法');
        
        // 备用清理方法：直接清除SharedPreferences
        SharedPreferences prefs = await SharedPreferences.getInstance();
        await prefs.clear();
        LogUtil.info('已使用备用方法清除SharedPreferences数据');
        
        // 清空消息跟踪器
        _cleanupMessageTracker();
      }
      
    } catch (e) {
      LogUtil.error('缓存清理失败: $e');
      // 不抛出异常，继续执行流程
    }
  }
  
  /// 清空消息跟踪器
  void _cleanupMessageTracker() {
    try {
      LogUtil.debug('清空消息跟踪器...');
      MessageTracker().clearAll();
    } catch (e) {
      LogUtil.error('清空消息跟踪器失败: $e');
    }
  }
  
  /// 清理UI状态
  Future<void> _cleanupUIState() async {
    try {
      // 清空会话服务数据
      if (Get.isRegistered<SessionService>()) {
        try {
          final sessionService = Get.find<SessionService>();
          await sessionService.clearCache();
          LogUtil.debug('已清空会话服务数据');
        } catch (e) {
          LogUtil.error('清空会话服务数据失败: $e');
        }
      }
      
    } catch (e) {
      LogUtil.error('清理UI状态失败: $e');
    }
  }
  
  /// 获取当前用户Token
  Future<String?> getIdToken({bool forceRefresh = false}) async {
    // 生成此次请求的唯一ID
    final requestId = '${DateTime.now().millisecondsSinceEpoch}_${math.Random().nextInt(1000)}';
    
    try {
      // 基于时间的去重检查 - 防止短时间内重复获取
      final now = DateTime.now().millisecondsSinceEpoch;
      final timeSinceLastFetch = now - _lastTokenFetchTime;
      
      if (!forceRefresh && timeSinceLastFetch < _tokenFetchMinInterval) {
        // 如果距离上次获取时间太短，使用缓存Token
        LogUtil.debug('距离上次获取Token时间太短(${timeSinceLastFetch}ms < ${_tokenFetchMinInterval}ms)，使用缓存Token');
        return _globalState.accessToken.value;
      }
      
      // 如果已有Token获取操作在进行中，等待操作完成
      if (_isTokenFetchInProgress) {
        LogUtil.debug('已有Token获取操作在进行中，等待操作完成');

        // 标记为待处理请求
        _pendingTokenRequests[requestId] = true;

        // 使用简单的轮询等待
        while (_isTokenFetchInProgress && _pendingTokenRequests.containsKey(requestId)) {
          await Future.delayed(const Duration(milliseconds: 50));
        }

        return _tokenResult.value;
      }
      
      // 标记获取操作开始
      _isTokenFetchInProgress = true;
      _lastTokenFetchTime = now;

      // 更新响应式状态
      isTokenFetching.value = true;
      lastTokenFetchTime.value = DateTime.fromMillisecondsSinceEpoch(now);
      
      // 最大重试次数
      const int maxRetries = 3;
      
      // 检查当前Token是否即将过期（默认如果当前时间超过token有效期的80%就刷新）
      bool shouldRefreshToken = forceRefresh;
      
      // 首先检查Token有效期
      if (!forceRefresh && _globalState.accessToken.value != null) {
        shouldRefreshToken = await _isTokenExpiringSoon();
        if (shouldRefreshToken) {
          LogUtil.info('当前Token即将过期，将强制刷新');
          forceRefresh = true;
        }
      }
      
      final firebaseUser = _auth.currentUser;
      
      // 如果没有登录用户，返回null
      if (firebaseUser == null) {
        LogUtil.warn('获取Token失败：当前没有登录用户');
        _isTokenFetchInProgress = false;
        _resolveAllPendingRequests(null);
        return null;
      }
      
      // 获取token，使用指数退避重试机制
      String? token;
      int attempt = 0;
      bool success = false;
      
      while (!success && attempt < maxRetries) {
        try {
          // 计算退避延迟（第一次尝试无延迟）
          if (attempt > 0) {
            final backoffDelay = _calculateBackoffDelay(attempt);
            LogUtil.info('获取Token第${attempt + 1}次尝试，等待${backoffDelay}毫秒后重试');
            await Future.delayed(Duration(milliseconds: backoffDelay));
          }
          
          // 使用Firebase内置方法获取token
          token = await firebaseUser.getIdToken(forceRefresh);
          LogUtil.debug('已${forceRefresh ? '强制刷新' : '获取'}token，长度: ${token?.length ?? 0}，尝试次数: ${attempt + 1}');
          success = true;
        } catch (e) {
          attempt++;
          LogUtil.error('Firebase获取Token失败(${attempt}/${maxRetries}): $e');
          
          // 最后一次尝试失败时，尝试使用缓存的token
          if (attempt >= maxRetries) {
            LogUtil.warn('已达到最大重试次数($maxRetries)，尝试使用缓存的token');
            token = _globalState.accessToken.value;
            if (token != null && token.isNotEmpty) {
              LogUtil.debug('使用缓存的token作为回退，长度: ${token.length}');
            }
          }
        }
      }
      
      // 如果获取到新token，更新全局状态
      if (token != null && token.isNotEmpty) {
        final currentToken = _globalState.accessToken.value;
        
        // 如果token发生变化或强制刷新，更新全局状态
        if (token != currentToken || forceRefresh) {
          LogUtil.debug('设置新token到全局状态，长度: ${token.length}');
          await _globalState.setAccessToken(token);
          
          // 更新token刷新时间
          await _updateTokenRefreshTime();
          
          // 同时保存到安全存储
          await _secureStorage.saveToken(token);
          LogUtil.debug('新token已保存到安全存储');
          
          // 强制刷新时，考虑通知WebSocket重连
          if ((forceRefresh || shouldRefreshToken) && !_isReconnectionNotified) {
            // 短暂延迟后触发WebSocket重连
            await Future.delayed(const Duration(milliseconds: 200));
            _notifyWebSocketReconnect();
          }
        } else {
          LogUtil.debug('token未变化，不重复设置到全局状态');
        }
      } else {
        LogUtil.warn('无法获取有效的token');
      }
      
      // 完成所有待处理的请求
      _resolveAllPendingRequests(token);
      
      // 标记获取操作结束
      _isTokenFetchInProgress = false;

      // 更新响应式状态
      isTokenFetching.value = false;

      return token;
    } catch (e) {
      LogUtil.error('获取Token失败: $e');
      ErrorHandler.handleException(
        AuthException('failed to get auth token', originalError: e, code: ErrorCodes.AUTH_ERROR),
        showSnackbar: false,
      );
      
      // 标记获取操作结束
      _isTokenFetchInProgress = false;

      // 更新响应式状态
      isTokenFetching.value = false;
      
      // 从待处理请求中移除当前请求
      _pendingTokenRequests.remove(requestId);
      
      // 处理其他待处理请求
      _resolveAllPendingRequests(null);
      
      return null;
    }
  }
  
  /// 解决所有待处理的Token请求 - 使用GetX响应式变量
  void _resolveAllPendingRequests(String? token) {
    // 设置Token结果
    _tokenResult.value = token;

    // 清空待处理请求
    _pendingTokenRequests.clear();
  }
  
  /// 计算重试退避延迟（毫秒）
  int _calculateBackoffDelay(int attempt) {
    // 基础延迟时间（毫秒）
    const baseDelayMs = 300;
    
    // 使用指数退避策略，随着重试次数增加延迟时间
    // 添加随机抖动以避免多个客户端同时重试
    final maxJitter = math.pow(2, attempt) * baseDelayMs * 0.2;
    final jitter = math.Random().nextInt(maxJitter.toInt());
    
    return (math.pow(2, attempt) * baseDelayMs).toInt() + jitter;
  }
  
  /// 检查Token是否即将过期
  Future<bool> _isTokenExpiringSoon() async {
    try {
      // Token预估有效期（通常为1小时）
      const tokenValidityMinutes = 60;
      
      // 获取上次刷新时间
      final lastRefreshTime = await _getTokenRefreshTime();
      if (lastRefreshTime == null) {
        // 如果没有记录刷新时间，返回true以促使刷新
        return true;
      }
      
      // 计算已经过去的时间（分钟）
      final now = DateTime.now();
      final elapsedMinutes = now.difference(lastRefreshTime).inMinutes;
      
      // 如果已经过去的时间超过有效期的80%，判定为即将过期
      final expiryThreshold = (tokenValidityMinutes * 0.8).toInt();
      final isExpiringSoon = elapsedMinutes >= expiryThreshold;
      
      if (isExpiringSoon) {
        LogUtil.info('Token已使用${elapsedMinutes}分钟，超过阈值${expiryThreshold}分钟，需要刷新');
      } else {
        LogUtil.debug('Token仍然有效，已使用${elapsedMinutes}分钟，阈值${expiryThreshold}分钟');
      }
      
      return isExpiringSoon;
    } catch (e) {
      LogUtil.error('检查Token有效期失败: $e');
      // 发生错误时为安全起见返回true，促使刷新
      return true;
    }
  }
  
  /// 获取Token上次刷新时间
  Future<DateTime?> _getTokenRefreshTime() async {
    try {
      // 从安全存储获取上次刷新时间
      final refreshTimeStr = await _secureStorage.getData('token_refresh_time');
      if (refreshTimeStr == null || refreshTimeStr.isEmpty) {
        return null;
      }
      
      // 解析时间戳
      final refreshTimestamp = int.tryParse(refreshTimeStr);
      if (refreshTimestamp == null) {
        return null;
      }
      
      return DateTime.fromMillisecondsSinceEpoch(refreshTimestamp);
    } catch (e) {
      LogUtil.error('获取Token刷新时间失败: $e');
      return null;
    }
  }
  
  /// 更新Token刷新时间
  Future<void> _updateTokenRefreshTime() async {
    try {
      // 将当前时间作为刷新时间记录
      final now = DateTime.now().millisecondsSinceEpoch;
      await _secureStorage.saveData('token_refresh_time', now.toString());
      LogUtil.debug('已更新Token刷新时间: ${DateTime.now()}');
    } catch (e) {
      LogUtil.error('更新Token刷新时间失败: $e');
    }
  }
  
  /// 获取当前用户
  Future<app.User?> getCurrentUser() async {
    try {
      final firebaseUser = _auth.currentUser;
      if (firebaseUser == null) {
        LogUtil.info('当前没有登录用户');
        return null;
      }
      
      final user = app.User.fromFirebaseUser(firebaseUser);
      _globalState.setCurrentUser(user);
      
      // 获取并设置Token - 不需要在此处重复设置token，统一由getIdToken方法处理
      // 只获取token但不设置到全局状态，避免重复设置
      await firebaseUser.getIdToken();
      
      return user;
    } catch (e) {
      LogUtil.error('获取当前用户失败: $e');
      ErrorHandler.handleException(
        AuthException('failed to get user information', originalError: e, code: ErrorCodes.AUTH_ERROR),
        showSnackbar: false,
      );
      return null;
    }
  }
  
  /// 匿名登录
  Future<UserCredential> signInAnonymously() async {
    try {
      final credential = await _repository.signInAnonymously();
      
      // 使用通用的登录后处理方法
      await _processAfterLogin(credential.user);
      
      return credential;
    } catch (e) {
      LogUtil.error('匿名登录失败: $e');
      ErrorHandler.handleException(
        AuthException('failed to anonymous login', originalError: e, code: ErrorCodes.AUTH_ERROR),
        showSnackbar: true,
      );
      rethrow;
    }
  }
  
  /// 确保匿名登录
  ///
  /// 如果当前没有用户登录，则进行匿名登录
  /// 如果已有用户登录，则保持当前登录状态
  Future<app.User?> ensureAnonymousLogin() async {
    try {
      final firebaseUser = await _repository.ensureAnonymousLogin();
      if (firebaseUser != null) {
        // 创建应用用户对象
        final user = app.User.fromFirebaseUser(firebaseUser);
        
        // 统一处理匿名登录后的状态更新和WebSocket重连
        await _handleAnonymousLoginSuccess(firebaseUser, user);
        
        return user;
      }
      return null;
    } catch (e) {
      LogUtil.error('确保匿名登录失败: $e');
      ErrorHandler.handleException(
        AuthException('failed to ensure anonymous login', originalError: e, code: ErrorCodes.AUTH_ERROR),
        showSnackbar: false,
      );
      return null;
    }
  }
  
  /// 统一处理匿名登录成功后的状态更新和WebSocket重连
  Future<void> _handleAnonymousLoginSuccess(User firebaseUser, app.User appUser) async {
    try {
      LogUtil.info('处理匿名登录成功，用户ID: ${appUser.uid}');
      
      // 1. 更新全局用户状态
      _globalState.setCurrentUser(appUser);
      
      // 2. 获取并设置token
      final token = await firebaseUser.getIdToken(true);
      if (token != null && token.isNotEmpty) {
        await _globalState.setAccessToken(token);
        LogUtil.debug('匿名登录后已设置token，长度: ${token.length}');
      } else {
        LogUtil.error('匿名登录后获取token失败');
      }
      
      // 3. 短暂延迟确保token已完全应用
      await Future.delayed(const Duration(milliseconds: 300));
      
      // 4. 更新角色会话绑定
      await _updateRoleSessionBindings();
      
      // 5. 刷新推荐数据
      await _refreshRecommendDataAfterLogin();
      
      // 6. 刷新会话列表
      await _refreshSessionsAfterLogin();
      
      // 7. 触发WebSocket重连
      LogUtil.info('匿名登录成功后触发WebSocket重连');
      _notifyWebSocketReconnect();
    } catch (e) {
      LogUtil.error('处理匿名登录成功后的状态更新失败: $e');
    }
  }

  /// 确保登出后有匿名用户登录
  Future<void> _ensureAnonymousLoginAfterSignOut() async {
    // 等待一小段时间，确保登出操作完成
    await Future.delayed(const Duration(milliseconds: 300));
    final anonymousUser = await ensureAnonymousLogin();
    
    // 匿名登录成功后的处理已经在_handleAnonymousLoginSuccess方法中统一处理，不需要在这里重复
    if (anonymousUser == null) {
      LogUtil.error('登出后匿名登录失败');
    }
  }
  
  /// 登录成功后刷新推荐数据
  /// 
  /// 确保有token后，在任何登录方式成功后调用此方法刷新推荐数据
  Future<void> _refreshRecommendDataAfterLogin() async {
    try {
      // 确保有token后再刷新推荐数据
      if (_globalState.accessToken.isNotEmpty) {
        LogUtil.info('登录成功后开始刷新推荐角色数据');
        
        // 触发刷新推荐数据事件
        GlobalEventState.to.triggerRefreshRecommendData(
          forceRefresh: true,
          source: 'login_success'
        );
        LogUtil.info('已发送推荐数据刷新事件');
        
        // 1. 刷新推荐服务的数据
        if (Get.isRegistered<RecommendService>()) {
          final recommendService = Get.find<RecommendService>();
          // 强制刷新推荐数据
          final result = await recommendService.refreshRoles(forceRefresh: true);
          LogUtil.info('登录成功后刷新推荐角色数据' + (result ? '成功' : '失败'));
        } else {
          LogUtil.debug('跳过刷新推荐数据：RecommendService未注册');
        }
        
        // 2. 初始化推荐控制器
        if (Get.isRegistered<RecommendController>()) {
          final recommendController = Get.find<RecommendController>();
          recommendController.initializeData();
          // 强制刷新推荐控制器数据
          recommendController.loadRecommendedRoles(forceRefresh: true);
          LogUtil.debug('已初始化并刷新推荐控制器数据');
        }
      } else {
        LogUtil.debug('跳过刷新推荐数据：无token');
      }
    } catch (e) {
      LogUtil.error('刷新推荐数据失败: $e');
    }
  }
  
  /// 更新用户状态（仅用于认证状态监听器）
  void _updateUserState(User? firebaseUser) {
    if (firebaseUser != null) {
      final user = app.User.fromFirebaseUser(firebaseUser);
      final oldUserId = _globalState.currentUser.value?.uid;
      
      _globalState.setCurrentUser(user);
      
      // 对于认证状态监听器触发的更新，获取token但不强制刷新
      // 避免与登录流程中的token处理冲突
      getIdToken(forceRefresh: false).then((token) {
        // token已经在getIdToken方法中设置到全局状态
        LogUtil.debug('认证状态监听器更新token完成');
        
        // 用户ID变更时触发WebSocket重连
        if (oldUserId != user.uid) {
          LogUtil.info('认证状态监听器检测到用户ID变更，触发WebSocket重连: $oldUserId -> ${user.uid}');
          _notifyWebSocketReconnect();
        }
      }).catchError((e) {
        LogUtil.error('认证状态监听器更新token失败: $e');
      });
    }
  }
  
  // 在LoginService类中添加一个标志变量防止重复触发WebSocket重连
  bool _isReconnectionNotified = false;

  /// 通知WebSocket重连
  void _notifyWebSocketReconnect() {
    try {
      // 如果重连通知标志位已设置，跳过
      if (_isReconnectionNotified) {
        LogUtil.debug('重连通知标志已设置，跳过重复通知');
        return;
      }
      
      // 获取当前用户ID
      final user = _auth.currentUser;
      final userId = user?.uid;
      
      // 如果没有用户ID，跳过
      if (userId == null || userId.isEmpty) {
        LogUtil.warn('无法通知WebSocket重连：没有当前用户ID');
        return;
      }
      
      // 如果没有有效Token，跳过
      final token = _globalState.accessToken.value;
      if (token == null || token.isEmpty) {
        LogUtil.warn('当前无有效Token，跳过WebSocket重连');
        return;
      }
      
      _isReconnectionNotified = true;
      
      // 修改为不再直接触发WebSocket重连
      // 统一通过auth_process_completed事件触发
      LogUtil.info('Token已更新，WebSocket重连将由认证流程完成事件统一处理');
      
      // 触发用户身份变更事件
      GlobalEventState.to.triggerUserIdentityChanged(userId: userId);
      
      // 设置一个延迟，在此期间不允许再次触发重连
      Future.delayed(const Duration(seconds: 5), () {
        _isReconnectionNotified = false;
      });
    } catch (e) {
      LogUtil.error('通知WebSocket重连失败: $e');
      _isReconnectionNotified = false;
    }
  }
  
  /// 重置AI回复状态
  void _resetAiReplyingStates({int? roleId}) {
    try {
      // 触发AI回复重置事件
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final eventId = 'reset_${timestamp}_${math.Random().nextInt(1000)}'; // 添加随机数确保唯一性

      GlobalEventState.to.triggerAiReplyReset(eventId: eventId, roleId: roleId);
      
      LogUtil.debug('已触发AI回复状态重置事件(ID:$eventId, roleId:$roleId)');
    } catch (e) {
      LogUtil.error('触发AI回复状态重置事件失败: $e');
    }
  }
  
  /// 验证Token并通知
  Future<bool> _validateTokenAndNotify(String token, String userId) async {
    try {
      // 验证token是否有效
      if (token.isEmpty) {
        LogUtil.warn('Token验证失败：token为空');
        return false;
      }
      
      // 验证token是否属于当前用户
      final currentFirebaseUser = _auth.currentUser;
      if (currentFirebaseUser == null || currentFirebaseUser.uid != userId) {
        LogUtil.warn('Token验证失败：当前用户与目标用户不匹配');
        return false;
      }
      
      // 触发令牌验证事件（暂时注释，后续可能需要添加到GlobalEventState）
      // GlobalEventState.to.triggerTokenValidated(userId: userId, tokenLength: token.length);
      
      return true;
    } catch (e) {
      LogUtil.error('Token验证失败: $e');
      return false;
    }
  }
  
  /// 监听认证状态变化
  void listenAuthStateChanges() {
    
    // 使用idTokenChanges监听器完全替代authStateChanges功能
    _auth.idTokenChanges().listen((User? user) async {
      // 记录认证状态变化
      LogUtil.info('认证状态变化(idTokenChanges): ${user?.uid ?? '未登录'}, 是否匿名: ${user?.isAnonymous ?? false}');
      
      // 原authStateChanges功能
      if (user == null) {
        // 用户登出
        await _globalState.clearCurrentUser();
        
        // 确保应用始终有匿名用户登录
        _ensureAnonymousLoginAfterSignOut();
      } else {
        // 用户登录或切换
        _updateUserState(user);
        
        // Token处理 - 合并idTokenChanges的原有功能
        final oldToken = _globalState.accessToken.value;
        
        // 使用统一方法获取token
        final token = await getIdToken(forceRefresh: false);
        
        // 如果token变化，记录日志
        if (token != null && oldToken != token) {
          LogUtil.debug('Token通过idTokenChanges监听器更新');
        }
      }
    });
  }
  
  /// 检查网络连接
  Future<bool> _checkInternetConnection() async {
    try {
      final connectivityResult = await connectivity.Connectivity().checkConnectivity();
      return connectivityResult != connectivity.ConnectivityResult.none;
    } catch (e) {
      LogUtil.error('检查网络连接失败: $e');
      return true; // 默认假设连接正常
    }
  }
  
  /// 获取Firebase错误消息
  String _getFirebaseErrorMessage(FirebaseAuthException e) {
    switch (e.code) {
      // 账号相关错误
      case 'account-exists-with-different-credential':
        return 'This email is associated with another login method, please try another method';
      case 'invalid-credential':
        return 'Invalid login credentials, please try again';
      case 'operation-not-allowed':
        return 'This login method is not enabled, please contact support';
      case 'user-disabled':
        return 'This account has been disabled, please contact support';
      case 'user-not-found':
        return 'No account found with this email address';
      
      // 邮箱相关错误
      case 'invalid-email':
        return 'The email address format is invalid';
      case 'email-already-in-use':
        return 'This email address is already in use by another account';
      
      // 密码相关错误
      case 'wrong-password':
        return 'Incorrect password, please try again';
      case 'weak-password':
        return 'Password is too weak, please use a stronger password';
      case 'requires-recent-login':
        return 'This operation requires recent authentication, please log in again';
      
      // 网络相关错误
      case 'network-request-failed':
        return 'Network request failed, please check your connection';
      case 'too-many-requests':
        return 'Too many requests, please try again later';
      
      // 验证相关错误
      case 'invalid-verification-code':
        return 'Invalid verification code';
      case 'invalid-verification-id':
        return 'Invalid verification ID';
      
      // 默认错误
      default:
        return e.message ?? 'Login failed, please try again';
    }
  }
  
  /// 将Firebase错误映射到应用错误码
  String _mapFirebaseErrorToCode(FirebaseAuthException e) {
    switch (e.code) {
      case 'network-request-failed':
        return ErrorCodes.NETWORK_ERROR;
      case 'too-many-requests':
        return ErrorCodes.RATE_LIMIT_ERROR;
      case 'user-not-found':
      case 'wrong-password':
      case 'invalid-email':
      case 'invalid-credential':
        return ErrorCodes.AUTH_ERROR;
      case 'email-already-in-use':
        return ErrorCodes.DUPLICATE_REQUEST;
      case 'weak-password':
        return ErrorCodes.BUSINESS_ERROR;
      default:
        return ErrorCodes.AUTH_ERROR;
    }
  }

  /// 邮箱密码登录
  Future<UserCredential> loginWithEmailPassword(String email, String password) async {
    try {
      // 检查网络连接
      bool isOnline = await _checkInternetConnection();
      if (!isOnline) {
        throw AuthException('network connection failed, please check the network settings and try again', code: ErrorCodes.NETWORK_ERROR);
      }
      
      // 清空所有缓存数据
      await _cleanupCaches();
      
      final credential = await _repository.loginWithEmailPassword(email, password);
      
      // 登录后处理
      await _processAfterLogin(credential.user);
      
      LogUtil.info('邮箱密码登录成功，用户ID: ${credential.user?.uid}');
      return credential;
    } catch (e) {
      LogUtil.error('Email password login failed: $e');
      if (e is FirebaseAuthException) {
        final errorCode = e.code;
        
        // 针对常见的登录失败情况提供更清晰的提示
        if (errorCode == 'user-not-found' || errorCode == 'wrong-password') {
          // 使用明确的英文提示，说明邮箱或密码不正确
          ErrorHandler.showInfo(
            'Incorrect email or password. Please try again.',
            title: 'Login Failed',
            duration: const Duration(seconds: 3),
          );
          
          // 同时记录异常以便日志跟踪，但不显示额外的snackbar
          ErrorHandler.handleException(
            AuthException('Login failed: Incorrect email or password', 
                       originalError: e, 
                       code: ErrorCodes.AUTH_ERROR),
            showSnackbar: false,
          );
        } else {
          // 其他类型的错误使用原有的错误处理
          final errorMessage = _getFirebaseErrorMessage(e);
          final errorCode = _mapFirebaseErrorToCode(e);
          
          ErrorHandler.handleException(
            AuthException('Login failed: $errorMessage', 
                       originalError: e, 
                       code: errorCode),
            showSnackbar: true,
          );
        }
      } else {
        ErrorHandler.handleException(
          AuthException('Login failed', originalError: e, code: ErrorCodes.AUTH_ERROR),
          showSnackbar: true,
        );
      }
      rethrow;
    }
  }
  
  /// 游客账号升级 - 绑定邮箱成为正式账号
  Future<UserCredential> upgradeAnonymousUserWithEmail(String email, String password) async {
    try {
      // 检查网络连接
      bool isOnline = await _checkInternetConnection();
      if (!isOnline) {
        throw AuthException('network connection failed, please check the network settings and try again', code: ErrorCodes.NETWORK_ERROR);
      }
      
      // 清空消息跟踪器，避免消息残留
      _cleanupMessageTracker();
      
      final currentUser = _auth.currentUser;
      
      // 验证当前用户状态
      if (currentUser == null) {
        throw AuthException('No user currently logged in', code: 'no-current-user');
      }
      
      if (!currentUser.isAnonymous) {
        throw AuthException('Current user is already registered', code: 'not-anonymous-user');
      }
      
      // 使用repository的注册方法，它会处理匿名用户升级的逻辑
      final userCredential = await _repository.registerWithEmailPassword(email, password);
      
      // 登录后处理
      await _processAfterLogin(userCredential.user);
      
      LogUtil.info('账号升级成功: ${userCredential.user?.uid}');
      return userCredential;
    } catch (e) {
      LogUtil.error('游客账号升级失败: $e');
      
      // 直接显示"邮箱已被注册"的明确提示
      if (e.toString().contains('该邮箱已被注册') || 
          e.toString().contains('email-already-in-use')) {
        // 使用更友好的错误消息直接显示给用户
        final errorMsg = 'This email is already registered. Please use another email or login directly.';
        
        // 使用showInfo显示明确的业务错误消息
        ErrorHandler.showInfo(
          errorMsg,
          title: 'Email Already Exists',
          duration: const Duration(seconds: 5),
        );
        
        // 同时记录异常以便日志跟踪
        ErrorHandler.handleException(
          AppException(errorMsg, code: ErrorCodes.DUPLICATE_REQUEST, originalError: e),
          showSnackbar: false, // 不再显示重复的snackbar
        );
        
        rethrow;
      } else if (e is AuthException) {
        ErrorHandler.handleException(e, showSnackbar: true);
      } else if (e is FirebaseAuthException) {
        final errorMessage = _getFirebaseErrorMessage(e);
        final errorCode = _mapFirebaseErrorToCode(e);
        
        ErrorHandler.handleException(
          AuthException('Registration failed: $errorMessage', 
                     originalError: e, 
                     code: errorCode),
          showSnackbar: true,
        );
      } else {
        ErrorHandler.handleException(
          AuthException('Registration failed', originalError: e, code: ErrorCodes.AUTH_ERROR),
          showSnackbar: true,
        );
      }
      rethrow;
    }
  }
  /// 检查用户状态
  Map<String, dynamic> checkUserUpgradeStatus() {
    final currentUser = _auth.currentUser;

    return {
      'hasUser': currentUser != null,
      'isGuest': currentUser?.isAnonymous ?? false,
      'canUpgrade': currentUser?.isAnonymous ?? false,
      'userInfo': currentUser != null ? {
        'uid': currentUser.uid,
        'email': currentUser.email,
        'displayName': currentUser.displayName,
      } : null,
    };
  }

  /// 获取当前Token获取状态（响应式访问）
  bool get isCurrentlyFetchingToken => isTokenFetching.value;

  /// 获取最后Token获取时间（响应式访问）
  DateTime? get lastTokenFetchDateTime => lastTokenFetchTime.value;

  /// 监听Token获取状态变化
  Stream<bool> get tokenFetchingStream => isTokenFetching.stream;

  /// 监听最后Token获取时间变化
  Stream<DateTime?> get lastTokenFetchTimeStream => lastTokenFetchTime.stream;
}